{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@pages/*": ["src/pages/*"], "@hooks/*": ["src/hooks/*"], "@utils/*": ["src/utils/*"], "@styles/*": ["src/styles/*"], "@assets/*": ["src/assets/*"], "@api/*": ["src/api/*"]}}, "include": ["src", "netlify/functions"], "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.node.json"}]}