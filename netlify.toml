[build]
  command = "npm run build"
  publish = "dist"
  functions = "netlify/functions"

[functions]
  node_bundler = "esbuild"
  external_node_modules = ["@firebase/firestore", "@firebase/database", "@google-cloud/firestore"]

[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[dev]
  framework = "vite"
  targetPort = 5173

[build.environment]
  NODE_VERSION = "18"
