{"name": "magica-suite-api", "version": "1.0.0", "description": "Backend API for MagicaSuite", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "lint": "eslint .", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "setup:firebase": "node scripts/setup-firebase.js", "setup:sentry": "node scripts/setup-sentry.js", "deploy": "node scripts/deploy.js", "build": "echo 'No build step required'", "docker:redis": "docker-compose up -d redis"}, "dependencies": {"@sentry/node": "7.91.0", "@sentry/profiling-node": "1.2.6", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "7.1.5", "firebase-admin": "^12.0.0", "helmet": "7.1.0", "ioredis": "5.3.2", "morgan": "^1.10.0", "stripe": "14.10.0", "winston": "3.11.0", "winston-daily-rotate-file": "4.7.1", "zod": "3.22.4"}, "devDependencies": {"@babel/core": "7.23.6", "@babel/preset-env": "7.23.6", "@types/jest": "29.5.11", "@types/supertest": "6.0.2", "babel-jest": "29.7.0", "eslint": "8.56.0", "jest": "29.7.0", "nodemon": "3.0.2", "supertest": "6.3.3"}, "jest": {"testEnvironment": "node", "transform": {"^.+\\.js$": "babel-jest"}, "collectCoverageFrom": ["**/*.{js,jsx}", "!**/node_modules/**", "!**/coverage/**"]}}