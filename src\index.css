@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-gray-900 text-white;
    font-family: 'Inter', sans-serif;
  }
}

@layer utilities {
  .animated-gradient {
    background: linear-gradient(
      -45deg,
      rgba(59, 130, 246, 0.15),
      rgba(139, 92, 246, 0.15),
      rgba(16, 185, 129, 0.15),
      rgba(249, 115, 22, 0.15)
    );
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
  }

  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  @keyframes magic-particle {
    0% {
      transform: translateY(0) scale(1);
      opacity: 1;
    }
    100% {
      transform: translateY(-20px) scale(0);
      opacity: 0;
    }
  }

  .animate-magic-particle {
    animation: magic-particle 1.5s ease-out forwards;
  }

  .animate-spin-slow {
    animation: spin 3s linear infinite;
  }

  @keyframes float {
    0% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
    100% {
      transform: translateY(0px);
    }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .magic-shadow {
    box-shadow: 0 0 30px -5px rgba(139, 92, 246, 0.3);
  }

  .magic-text-glow {
    text-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
  }

  .magic-border-glow {
    box-shadow: 0 0 15px -3px rgba(139, 92, 246, 0.4);
  }

  .magic-hover-glow:hover {
    box-shadow: 0 0 20px -5px rgba(139, 92, 246, 0.5);
    transform: translateY(-2px);
    transition: all 0.3s ease;
  }

  .animation-delay-2000 {
    animation-delay: 2s;
  }
  .animation-delay-4000 {
    animation-delay: 4s;
  }

  .floating {
    animation: floating 3s ease-in-out infinite;
  }

  @keyframes floating {
    0% {
      transform: translate(0, 0px);
    }
    50% {
      transform: translate(0, 15px);
    }
    100% {
      transform: translate(0, 0px);
    }
  }

  .glow {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
    animation: glow 3s ease-in-out infinite alternate;
  }

  @keyframes glow {
    from {
      box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
    }
    to {
      box-shadow: 0 0 30px rgba(139, 92, 246, 0.6);
    }
  }

  .particle {
    position: absolute;
    opacity: 0;
    animation: particle 3s ease-in infinite;
  }

  @keyframes particle {
    0% {
      transform: translateY(0) scale(0);
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
    100% {
      transform: translateY(-100px) scale(1);
      opacity: 0;
    }
  }

  /* Sparkle animation */
  @keyframes sparkle {
    0% {
      transform: scale(0) rotate(0deg);
      opacity: 0;
    }
    50% {
      transform: scale(1) rotate(180deg);
      opacity: 1;
    }
    100% {
      transform: scale(0) rotate(360deg);
      opacity: 0;
    }
  }

  .animate-sparkle {
    animation: sparkle 2s ease-in-out infinite;
  }

  /* Star animation */
  @keyframes star {
    0% {
      transform: scale(1) rotate(0deg);
    }
    50% {
      transform: scale(1.5) rotate(180deg);
    }
    100% {
      transform: scale(1) rotate(360deg);
    }
  }

  .animate-star {
    animation: star 3s ease-in-out infinite;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(139, 92, 246, 0.5);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(139, 92, 246, 0.7);
  }

  /* Backdrop blur utilities */
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }

  .backdrop-blur-sm {
    backdrop-filter: blur(4px);
  }

  .backdrop-blur-md {
    backdrop-filter: blur(8px);
  }

  .backdrop-blur-lg {
    backdrop-filter: blur(12px);
  }

  .backdrop-blur-xl {
    backdrop-filter: blur(16px);
  }

  /* Enhanced glass effects */
  .glass-effect {
    @apply relative overflow-hidden;
    &::before {
      content: '';
      @apply absolute inset-0;
      background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.05),
        rgba(255, 255, 255, 0.02)
      );
      backdrop-filter: blur(10px);
    }
    &::after {
      content: '';
      @apply absolute inset-0;
      background: linear-gradient(
        45deg,
        transparent,
        rgba(255, 255, 255, 0.03),
        transparent
      );
      animation: glass-shine 3s linear infinite;
    }
  }

  @keyframes glass-shine {
    0% {
      transform: translateX(-100%);
    }
    50%, 100% {
      transform: translateX(100%);
    }
  }

  /* Enhanced category effects */
  .category-active {
    position: relative;
    &::before {
      content: '';
      @apply absolute inset-0 rounded-lg opacity-30;
      background: radial-gradient(
        circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
        var(--category-color, rgba(139, 92, 246, 0.2)),
        transparent 70%
      );
      filter: blur(10px);
      transition: opacity 0.3s;
    }
    &:hover::before {
      opacity: 0.5;
    }
  }

  /* Enhanced particle effects */
  .magic-particle {
    @apply absolute rounded-full;
    background: radial-gradient(
      circle at center,
      currentColor,
      transparent 70%
    );
    animation: float-particle 2s ease-out infinite;
  }

  @keyframes float-particle {
    0% {
      transform: translate(0, 0) scale(1);
      opacity: 1;
    }
    100% {
      transform: translate(
        calc(var(--x, 0) * 30px),
        calc(var(--y, -1) * 30px)
      ) scale(0);
      opacity: 0;
    }
  }

  /* Enhanced glow animations */
  .animate-glow-pulse {
    animation: glow-pulse 2s ease-in-out infinite;
  }

  @keyframes glow-pulse {
    0%, 100% {
      opacity: 0.4;
      filter: blur(3px) brightness(0.9);
    }
    50% {
      opacity: 0.6;
      filter: blur(4px) brightness(1.1);
    }
  }

  .shadow-category {
    position: relative;
    &::after {
      content: '';
      @apply absolute inset-0 rounded-lg -z-10;
      background: var(--category-color, rgba(139, 92, 246, 0.2));
      filter: blur(20px);
      opacity: 0.3;
      transform-origin: center;
      animation: shadow-pulse 3s ease-in-out infinite;
    }
  }

  @keyframes shadow-pulse {
    0%, 100% {
      transform: scale(0.95);
      opacity: 0.3;
    }
    50% {
      transform: scale(1.05);
      opacity: 0.4;
    }
  }

  /* Enhanced hover effects */
  .hover-lift {
    transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    &:hover {
      transform: translateY(-2px) scale(1.02);
    }
  }

  /* Magical border animation */
  .magical-border-animation {
    position: relative;
    &::before {
      content: '';
      position: absolute;
      inset: -1px;
      padding: 1px;
      border-radius: 8px;
      background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0.03),
        var(--border-color, rgba(139, 92, 246, 0.15)),
        rgba(255, 255, 255, 0.03)
      );
      -webkit-mask: 
        linear-gradient(#fff 0 0) content-box, 
        linear-gradient(#fff 0 0);
      mask: 
        linear-gradient(#fff 0 0) content-box, 
        linear-gradient(#fff 0 0);
      -webkit-mask-composite: xor;
      mask-composite: exclude;
      opacity: 0.5;
    }
    &::after {
      content: '';
      position: absolute;
      inset: -1px;
      padding: 1px;
      border-radius: 8px;
      background: linear-gradient(
        90deg,
        transparent,
        var(--border-color, rgba(139, 92, 246, 0.15)),
        transparent
      );
      -webkit-mask: 
        linear-gradient(#fff 0 0) content-box, 
        linear-gradient(#fff 0 0);
      mask: 
        linear-gradient(#fff 0 0) content-box, 
        linear-gradient(#fff 0 0);
      -webkit-mask-composite: xor;
      mask-composite: exclude;
      opacity: 0;
      animation: border-shine 3s linear infinite;
    }
  }

  @keyframes border-shine {
    0% {
      opacity: 0;
      transform: translateX(-100%);
    }
    20%, 80% {
      opacity: 0.4;
    }
    100% {
      opacity: 0;
      transform: translateX(100%);
    }
  }
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-purple-500/50 rounded-full hover:bg-purple-400/50 transition-colors;
}

/* Custom backdrop blur for modals and overlays */
.backdrop-magical {
  backdrop-filter: blur(8px);
  background: linear-gradient(
    135deg,
    rgba(17, 24, 39, 0.7),
    rgba(88, 28, 135, 0.1)
  );
}